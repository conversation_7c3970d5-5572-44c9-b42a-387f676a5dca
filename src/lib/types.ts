// Edge-compatible logger interface
export interface EdgeLogger {
  info: (obj: any, msg?: string) => void;
  error: (obj: any, msg?: string) => void;
  warn: (obj: any, msg?: string) => void;
  debug: (obj: any, msg?: string) => void;
  trace: (obj: any, msg?: string) => void;
  fatal: (obj: any, msg?: string) => void;
}

export interface AppBindings {
  Variables: {
    logger: EdgeLogger;
  };
}

export interface CleanCodeRequest {
  code: string;
  imageUrl: string;
}

export interface CleanCodeResponse {
  cleanCode: string;
  suggestions?: string[];
}
