/* eslint-disable node/no-process-env */

// This file is only used for local development to load .env files
// It's not included in the Edge Runtime build

try {
  // Only load dotenv in Node.js environment for local development
  if (typeof process !== "undefined" && process.env.NODE_ENV !== "production") {
    // eslint-disable-next-line ts/no-require-imports
    const dotenv = require("dotenv");
    // eslint-disable-next-line ts/no-require-imports
    const dotenvExpand = require("dotenv-expand");
    
    dotenvExpand.expand(dotenv.config());
  }
} catch {
  // Ignore if dotenv is not available
}
