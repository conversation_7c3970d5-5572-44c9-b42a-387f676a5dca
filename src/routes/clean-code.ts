import type { CleanCodeRequest, CleanCodeResponse } from "@/lib/types";
import Anthropic from "@anthropic-ai/sdk";
import env from "@/env";
import { createRouter } from "@/lib/create-app";
import { prompt } from "@/lib/prompt";

const cleanCodeRoutes = createRouter();

cleanCodeRoutes.post("/", async (c) => {
  const { code, imageUrl } = await c.req.json<CleanCodeRequest>();

  try {
    const client = new Anthropic({ apiKey: env.ANTHROPIC_API_KEY });
    const base64Image = imageUrl.split(",")[1] || "";

    c.var.logger.info({
      event: "anthropic_api_call",
      imageDataLength: base64Image.length,
    }, "Calling Anthropic API");

    const res = await client.messages.create({
      model: "claude-3-5-sonnet-latest",
      max_tokens: 8192,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `${prompt}\nClean and optimize this code:\n${code}`,
            },
            {
              type: "image",
              source: {
                type: "base64",
                media_type: "image/png",
                data: base64Image,
              },
            },
          ],
        },
      ],
    });

    c.var.logger.info({
      event: "anthropic_api_response",
      inputTokens: res.usage.input_tokens,
      outputTokens: res.usage.output_tokens,
      contentBlocks: res.content.length,
    }, "Received response from Anthropic API");

    const textPart = res.content.find((c: any) => c.type === "text") as any;
    const cleanCode = textPart?.text || "";

    const response: CleanCodeResponse = {
      cleanCode,
      suggestions: [],
    };

    return c.json(response, 200);
  }
  catch (error) {
    const errorMessage
      = error instanceof Error ? error.message : "Unknown error";

    const resp: CleanCodeResponse = {
      cleanCode: "",
      suggestions: [errorMessage],
    };
    return c.json(resp, 500);
  }
});

export default cleanCodeRoutes;
