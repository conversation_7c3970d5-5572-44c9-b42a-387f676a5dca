{"name": "fiddle-anthropic-proxy", "scripts": {"start": "pnpm run build && npx vercel dev", "deploy": "pnpm run build:edge && npx vercel", "lint": "eslint .", "lint:fix": "eslint . --fix", "dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "build:edge": "tsc --project tsconfig.edge.json && tsc-alias"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@hono/node-server": "^1.14.3", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "hono": "^4.7.10", "hono-pino": "^0.8.0", "node-path": "^0.0.3", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "stoker": "^1.4.2", "zod": "^3.25.30"}, "devDependencies": {"@antfu/eslint-config": "^4.13.2", "@types/node": "^22.15.23", "@vitest/ui": "^3.1.4", "eslint": "^9.27.0", "eslint-plugin-format": "^1.0.1", "tsc-alias": "^1.8.16", "tsx": "^4.7.1", "typescript": "^5.8.3", "vercel": "^42.2.0", "vitest": "^3.1.4"}}