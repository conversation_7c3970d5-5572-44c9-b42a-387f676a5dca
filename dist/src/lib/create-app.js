import { Hono } from "hono";
import { notFound, onError, serveEmojiFavicon } from "stoker/middlewares";
import { auth } from "@/middlewares/auth";
import { honoCors } from "@/middlewares/hono-cors";
import { pinoLogger } from "@/middlewares/pino-logger";
export function createRouter() {
    return new Hono({ strict: false });
}
export default function createApp() {
    const app = new Hono({ strict: false });
    app.use(serveEmojiFavicon("📝"));
    app.use(honoCors());
    app.use(pinoLogger());
    app.use("/clean-code", async (c, next) => {
        await auth(c, next);
    });
    app.notFound(notFound);
    app.onError(onError);
    return app;
}
