import env from "@/env";
// Simple Edge-compatible logger implementation
function createEdgeLogger() {
    const logLevels = {
        fatal: 0,
        error: 1,
        warn: 2,
        info: 3,
        debug: 4,
        trace: 5,
    };
    const currentLevel = logLevels[env.LOG_LEVEL] ?? 3;
    function log(level, obj, msg) {
        if (logLevels[level] <= currentLevel) {
            const timestamp = new Date().toISOString();
            const logData = typeof obj === "object" ? obj : { message: obj };
            const message = msg || logData.message || "";
            const logEntry = JSON.stringify({
                level,
                time: timestamp,
                ...logData,
                msg: message,
            });
            // Use appropriate console method based on log level
            if (level === "fatal" || level === "error") {
                console.error(logEntry);
            }
            else if (level === "warn") {
                console.warn(logEntry);
            }
            else {
                // For info, debug, trace - use console.error as it's allowed
                console.error(logEntry);
            }
        }
    }
    return {
        fatal: (obj, msg) => log("fatal", obj, msg),
        error: (obj, msg) => log("error", obj, msg),
        warn: (obj, msg) => log("warn", obj, msg),
        info: (obj, msg) => log("info", obj, msg),
        debug: (obj, msg) => log("debug", obj, msg),
        trace: (obj, msg) => log("trace", obj, msg),
    };
}
// Check if we're in Edge Runtime or Node.js environment
function isEdgeRuntime() {
    return (typeof globalThis !== "undefined" && "EdgeRuntime" in globalThis)
        || (typeof process === "undefined")
        || (typeof process.versions === "undefined")
        || (typeof process.versions.node === "undefined");
}
export function pinoLogger() {
    if (isEdgeRuntime()) {
        // Use simple Edge-compatible logger
        const logger = createEdgeLogger();
        return async (c, next) => {
            const reqId = crypto.randomUUID();
            c.set("logger", logger);
            const start = Date.now();
            logger.info({
                reqId,
                method: c.req.method,
                url: c.req.url,
            }, "Request started");
            await next();
            const duration = Date.now() - start;
            logger.info({
                reqId,
                method: c.req.method,
                url: c.req.url,
                status: c.res.status,
                duration,
            }, "Request completed");
        };
    }
    else {
        // Use full Pino logger for Node.js environment
        try {
            // eslint-disable-next-line ts/no-require-imports
            const honoPino = require("hono-pino");
            // eslint-disable-next-line ts/no-require-imports
            const pino = require("pino");
            // eslint-disable-next-line ts/no-require-imports
            const pretty = require("pino-pretty");
            return honoPino.pinoLogger({
                pino: pino({
                    level: env.LOG_LEVEL || "info",
                }, env.NODE_ENV === "production" ? undefined : pretty()),
                http: {
                    reqId: () => crypto.randomUUID(),
                },
            });
        }
        catch {
            // Fallback to Edge logger if Pino is not available
            const logger = createEdgeLogger();
            return async (c, next) => {
                c.set("logger", logger);
                await next();
            };
        }
    }
}
