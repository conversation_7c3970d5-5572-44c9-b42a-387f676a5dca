import { cors } from "hono/cors";
export function honoCors() {
    return cors({
        origin: (origin) => {
            // Allow null origin (for Figma plugins and local files)
            if (!origin || origin === "null")
                return "*";
            // Allow localhost and any other origins
            return origin;
        },
        allowMethods: ["POST", "OPTIONS"],
        allowHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
        credentials: false,
    });
}
