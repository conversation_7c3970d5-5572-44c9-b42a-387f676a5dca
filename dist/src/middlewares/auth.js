import { verify } from "hono/jwt";
import env from "../env.js";
export async function auth(c, next) {
    const authHeader = c.req.header("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return c.json({ error: "Unauthorized" }, 401);
    }
    const token = authHeader.split(" ")[1];
    try {
        await verify(token, env.JWT_SECRET);
        await next();
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Authentication error";
        c.var.logger.error(errorMessage);
        return c.json({ error: "Invalid token" }, 401);
    }
}
