import createApp from "./lib/create-app.js";
import authRoutes from "./routes/auth.js";
import cleanCodeRoutes from "./routes/clean-code.js";
import debugRoutes from "./routes/debug.js";
import indexRoutes from "./routes/index.js";
const app = createApp();
app.route("/", indexRoutes);
app.route("/auth", authRoutes);
app.route("/clean-code", cleanCodeRoutes);
app.route("/error", debugRoutes);
export default app;
